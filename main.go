package main

import (
	"context"
	"encoding/json"
	"log"
	"log/slog"
	"net/http"
	"os"

	"github.com/alexflint/go-arg"
	"github.com/openai/openai-go"
	"github.com/santhosh-tekuri/jsonschema/v6"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/db"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const (
	jsonSchemaFilename        = "room-design.schema.json"
	defaultRoomLayoutFilename = "default_room_layout.json"
)

type Command string

const (
	CmdSrv Command = "srv"
	CmdRt  Command = "roundtrip"
)

type args struct {
	PgHost     string `arg:"env" default:"alpha-database.cin8ix3jru5g.us-west-2.rds.amazonaws.com"`
	PgDatabase string `arg:"env" default:"alpha-prototype"`
	Cmd        string `arg:"positional" default:"srv"`
}

func main() {
	ctx := context.Background()
	logger := setupLogger()
	schema := controllers.Schema(jsonSchemaFilename)

	defaultRoomLayout, err := os.ReadFile(defaultRoomLayoutFilename)
	if err != nil {
		log.Fatalf("Error reading default room layout JSON file: %v", err)
	}

	var args args
	arg.MustParse(&args)

	pgPassword, err := db.GetPostgresPassword(ctx)
	if err != nil {
		log.Fatal(err)
	}
	pgDb := setupDb(ctx, logger, args, pgPassword)
	defer pgDb.Close()

	textGenerator := gateways.NewOpenAI(openai.NewClient())
	cmdController := controllers.NewDesignMutationController(pgDb, textGenerator, logger)
	designQueryController := controllers.NewDesignAccessController(pgDb, logger)

	relDb := db.NewRelationalDb(ctx, args.PgHost, "projects", pgPassword, logger)
	defer relDb.Close()

	presetRetriever := usecases.NewPresetRetriever(relDb)
	presetRetrievalController := controllers.NewPresetRetrievalController(presetRetriever)
	designCreator := usecases.NewDesignCreater(relDb, textGenerator, logger)
	designUpdater := usecases.NewDesignUpdater(relDb)
	designSaver := usecases.NewDesignSaver(relDb)
	bulkDesignSaver := usecases.NewBulkDesignSaver(relDb, logger)
	designDeleter := usecases.NewDesignDeleter(relDb)
	writeController := controllers.NewDesignWriteController(
		designCreator, designUpdater, designSaver, bulkDesignSaver, designDeleter, logger)

	switch Command(args.Cmd) {
	case CmdSrv:
		startSrv(logger, schema, designQueryController, cmdController, defaultRoomLayout, presetRetrievalController)
	case CmdRt:
		roundTrip(ctx, logger, designQueryController, writeController)
	default:
		log.Fatalf("Unknown command: %s", args.Cmd)
	}
}

func setupLogger() *slog.Logger {
	removeTimeAttr := func(groups []string, a slog.Attr) slog.Attr {
		if a.Key == slog.TimeKey {
			return slog.Attr{}
		}
		return a
	}
	var programLevel = new(slog.LevelVar) // Info by default
	logHandler := slog.NewTextHandler(os.Stderr, &slog.HandlerOptions{
		Level: programLevel, ReplaceAttr: removeTimeAttr,
	})
	logger := slog.New(logHandler)
	slog.SetDefault(logger) // Set the global logger, since this is main.
	return logger
}

func setupDb(ctx context.Context, logger *slog.Logger, args args, pgPwd string) *gateways.Postgres {
	db := db.NewPostgres(ctx, args.PgHost, args.PgDatabase, pgPwd, logger)
	count, err := db.CountProjectsWithDesigns(ctx)
	if err != nil {
		db.Close()
		log.Fatal(err)
	}
	log.Printf("DB has %d projects with designs.\n", count)
	return db
}

func startSrv(logger *slog.Logger, schema *jsonschema.Schema,
	designQueryController *controllers.DesignAccessController, cmdController *controllers.DesignMutationController,
	defaultRoomLayout json.RawMessage, presetRetrievalController *controllers.PresetRetrievalController) {

	writeHandler := web.NewDesignMutationHandler(logger, schema, cmdController)
	designViewHandler := web.NewHttpGetReqHandler(logger, designQueryController)
	web.RegisterGlobalHandlers(designViewHandler)
	web.RegisterDesignHandlers("/projects/{projectId}", designViewHandler, writeHandler)
	presetViewHandler := web.NewPresetQueryHandler(logger, defaultRoomLayout, presetRetrievalController)
	web.RegisterPresetHandlers("/presets", presetViewHandler)

	log.Println("Server listening on :8080")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatal(err)
	}
}

type rewriter struct {
	logger        *slog.Logger
	cmdController *controllers.DesignWriteController
}

func (r *rewriter) PresentError(err error) {
	r.logger.Error("Error fetching designs", slog.String("error", err.Error()))
}

func (r *rewriter) ConveySuccess()                                      {}
func (r *rewriter) ConveySuccessWithNewResource(design usecases.Design) {}

func (r *rewriter) PresentData(ctx context.Context, data any)                     {}
func (r *rewriter) PresentDesign(ctx context.Context, design usecases.Design)     {}
func (r *rewriter) PresentDesigns(ctx context.Context, designs []usecases.Design) {}

func (r *rewriter) PresentDesignsByProject(ctx context.Context, data map[entities.ProjectId][]usecases.Design, errors []error) {
	for projectId, designs := range data {
		if projectId == "DEFAULT" {
			continue
		}
		output := make([]adapters.Design, len(designs))
		for i, design := range designs {
			d := adapters.FromUsecaseDesign(design)
			output[i] = presenters.UsePascalCaseTilePatterns(d)
		}
		r.cmdController.SaveDesignsForProject(ctx, projectId, output, r)
	}
}

func roundTrip(ctx context.Context, logger *slog.Logger, queryController *controllers.DesignAccessController, cmdController *controllers.DesignWriteController) {
	projectIds, err := queryController.IdsOfProjectsWithDesigns(ctx)
	if err != nil {
		log.Fatal(err)
	}
	rewriter := &rewriter{logger: logger, cmdController: cmdController}
	queryController.FetchDesignsForMultipleProjects(ctx, projectIds, rewriter)
}
