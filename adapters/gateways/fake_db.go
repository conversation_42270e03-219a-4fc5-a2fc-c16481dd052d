package gateways

import (
	"context"
	"slices"
	"time"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeRelDb is a fake implementation of the usecases.designRepository interface
// for use in unit tests.
type FakeRelDb struct {
	designs map[uuid.UUID]*usecases.Design
	projIdx map[entities.ProjectId][]*usecases.Design
}

func NewFakeRelDb() *FakeRelDb {
	return &FakeRelDb{
		designs: make(map[uuid.UUID]*usecases.Design),
		projIdx: make(map[entities.ProjectId][]*usecases.Design),
	}
}

func (r *FakeRelDb) UpsertDesign(ctx context.Context, design usecases.Design) (uuid.UUID, error) {
	var zeroUUID uuid.UUID
	if design.ID == zeroUUID || design.ID == uuid.Nil {
		design.ID = uuid.New()
	}
	design.LastUpdated = time.Now()
	if design.Created.IsZero() {
		design.Created = design.LastUpdated
	}
	r.designs[design.ID] = &design
	r.projIdx[design.ProjectID] = append(r.projIdx[design.ProjectID], &design)
	return design.ID, nil
}

func (r *FakeRelDb) ReadDesign(ctx context.Context, id uuid.UUID) (usecases.Design, error) {
	d, ok := r.designs[id]
	if !ok {
		return usecases.Design{}, usecases.ErrNotFound
	}
	return *d, nil
}

func (m *FakeRelDb) DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]usecases.Design, error) {
	results, ok := m.projIdx[projectId]
	if !ok {
		return nil, usecases.ErrNotFound
	}
	designs := make([]usecases.Design, len(results))
	for i, d := range results {
		designs[i] = *d
	}
	return designs, nil
}

func (r *FakeRelDb) DesignsByProject(ctx context.Context, projectIDs []entities.ProjectId) (
	map[entities.ProjectId][]usecases.Design, []error, error) {
	results := make(map[entities.ProjectId][]usecases.Design)
	errors := []error{}
	for _, projectId := range projectIDs {
		designs, err := r.DesignsForProject(ctx, projectId)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		results[projectId] = designs
	}
	return results, errors, nil
}

func (r *FakeRelDb) DeleteDesign(ctx context.Context, id uuid.UUID) error {
	d := r.designs[id]
	if d == nil {
		return usecases.ErrNotFound
	}
	idx := slices.Index(r.projIdx[d.ProjectID], d)
	if idx < 0 {
		return usecases.ErrNotFound
	}
	r.projIdx[d.ProjectID] = slices.Delete(r.projIdx[d.ProjectID], idx, idx+1)
	delete(r.designs, id)
	return nil
}
