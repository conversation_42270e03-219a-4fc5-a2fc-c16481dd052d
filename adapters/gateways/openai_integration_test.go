//go:build integration

// Package gateways_test contains integration tests for the OpenAI gateway implementation.
// These only test error-handling so they need to be run *without* OpenAI API credentials:
// OPENAI_API_KEY="" go test -v --tags=integration -count=1 ./adapters/gateways
package gateways_test

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/openai/openai-go"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Test buildProductDescriptions indirectly by testing the behavior when HTTP calls fail
func TestGenerateDesignTitleAndDescription_HTTPError(t *testing.T) {
	// Note: This test demonstrates the structure but makes real HTTP calls
	// In a production test environment, you'd want to mock the HTTP client
	design := createTestDesign()

	client := openai.NewClient()
	openAI := gateways.NewOpenAI(client)

	ctx := t.Context()

	// This will make real HTTP calls to the catalog API and then fail on OpenAI API
	// since we don't have valid credentials
	_, _, err := openAI.GenerateDesignTitleAndDescription(ctx, design)
	assert.Error(t, err)
}

func TestGenerateDesignTitleAndDescription_EmptyDesign(t *testing.T) {
	design := createEmptyDesign()

	client := openai.NewClient()
	openAI := gateways.NewOpenAI(client)

	ctx := t.Context()

	// With an empty design (no products), buildProductDescriptions should return empty map
	// and the function should proceed to OpenAI call with empty product descriptions
	_, _, err := openAI.GenerateDesignTitleAndDescription(ctx, design)
	assert.Error(t, err)
}

// Test constants are accessible and function structure
func TestGenerateDesignTitleAndDescription_Structure(t *testing.T) {
	// Test that the function works with a basic design and follows expected flow
	design := createTestDesign()
	client := openai.NewClient()
	openAI := gateways.NewOpenAI(client)

	ctx := t.Context()

	// This will make real HTTP calls to catalog API and then fail on OpenAI API
	_, _, err := openAI.GenerateDesignTitleAndDescription(ctx, design)
	assert.Error(t, err)
}

// Test with design containing all product types
func TestGenerateDesignTitleAndDescription_AllProducts(t *testing.T) {
	design := createTestDesignWithAllProducts()
	client := openai.NewClient()
	openAI := gateways.NewOpenAI(client)

	ctx := t.Context()

	// This tests that all product types are handled correctly in buildProductDescriptions
	_, _, err := openAI.GenerateDesignTitleAndDescription(ctx, design)
	assert.Error(t, err)
}

// Test with design containing only some products
func TestGenerateDesignTitleAndDescription_PartialProducts(t *testing.T) {
	design := createTestDesign()
	// Remove some products to test partial coverage
	design.Mirror = nil
	design.Paint = nil

	client := openai.NewClient()
	openAI := gateways.NewOpenAI(client)

	ctx := t.Context()

	_, _, err := openAI.GenerateDesignTitleAndDescription(ctx, design)
	assert.Error(t, err)
}

// Test with nil UUID pointers (empty design)
func TestGenerateDesignTitleAndDescription_NilUUIDs(t *testing.T) {
	design := usecases.Design{
		ID:                 uuid.New(),
		ProjectID:          entities.ProjectId("PRJ-NIL-TEST"),
		Created:            time.Now(),
		LastUpdated:        time.Now(),
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.NoWallTile,
		DesignOptions:      usecases.DesignOptions{},
	}

	client := openai.NewClient()
	openAI := gateways.NewOpenAI(client)

	ctx := t.Context()

	// With all nil products, buildProductDescriptions should return empty map
	// and still proceed with the OpenAI call
	_, _, err := openAI.GenerateDesignTitleAndDescription(ctx, design)
	assert.Error(t, err)
}

// Test edge cases for UUID handling
func TestDesignWithEdgeCaseUUIDs(t *testing.T) {
	// Test with zero UUID
	zeroUUID := uuid.UUID{}

	design := usecases.Design{
		ID:                 uuid.New(),
		ProjectID:          entities.ProjectId("PRJ-ZERO-UUID"),
		Created:            time.Now(),
		LastUpdated:        time.Now(),
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.NoWallTile,
		DesignOptions: usecases.DesignOptions{
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: &zeroUUID,
			},
		},
	}

	client := openai.NewClient()
	openAI := gateways.NewOpenAI(client)

	ctx := t.Context()

	// Should handle zero UUID gracefully in buildProductDescriptions
	_, _, err := openAI.GenerateDesignTitleAndDescription(ctx, design)
	assert.Error(t, err)
}
